package com.ruijing.store.order.api.search.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.enums.ProcessSpeciesEnum;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.general.dto.BaseReqDTO;
import com.ruijing.store.order.api.general.enums.OrderNestedEnum;

import java.io.Serializable;
import java.util.*;

/**
 * @description: 订单搜索 通用查询参数
 * @author: zhuk
 * @create: 2019-11-12 16:17
 **/
public class OrderSearchParamDTO extends BaseReqDTO implements Serializable {

    private static final long serialVersionUID = 6186096192187978825L;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 竞价单id
     */
    private String bidOrderId;

    /**
     * 订单IDList
     */
    private List<Integer> orderIdList;

    /**
     * 审批日志--审批状态
     */
    private List<String> approveStatusList;

    /**
     * 审批日志--审批人id
     */
    private String operatorId;


    /**
     * 结算单IDList
     */
    private List<Integer> statementIdList;

    /**
     * 订单时间 排序
     */
    private SortOrderEnum orderDateSort;

    /**
     * 订单状态 集合
     */
    private List<Integer> statusList;

    /**
     * 排除的 订单状态集合
     */
    private List<Integer> excludeStatusList = new ArrayList<>(3);

    /**
     * 结算状态集合
     */
    private List<Integer> statementStatusList;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单对接同步状态
     */
    private List<Integer> dockingStatus;

    /**
     * 订单号集合
     */
    private List<String> orderNoList;

    /**
     * 订单流程:0 线上单 1线下单
     */
    private ProcessSpeciesEnum species;

    /**
     * 采购申请单id 列表
     */
    private List<Integer> applicationIdList;

    /**
     * 排除的采购单id
     */
    private List<Integer> excludeApplicationIds;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门Id集合
     */
    private List<Integer> departmentIdList;

    /**
     * 单位代码
     */
    private String orgCode;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 是否全匹配单位名称，默认不是
     */
    private Boolean orgNameFullMatch;

    /**
     * 组织IdList
     */
    private List<Integer> orgIdList;

    /**
     * 排除的 订单状态集合
     */
    private List<Integer> excludeOrgIdList;

    /**
     * 采购人名称
     */
    private String buyerName;

    /**
     * 采购人Id集合
     */
    private List<Integer> buyerIdList;

    /**
     * 供应商名称
     */
    private String suppName;

    /**
     * 供应商Id集合
     */
    private List<Integer> suppIdList;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 商品货号
     */
    private String goodsCode;

    /**
     * cas 号
     */
    private String casNo;


    /**
     * 品牌id
     */
    private Integer brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 商品id
     */
    private String productSn;

    /**
     * 经费卡id集合
     */
    private List<String> cardIdList;

    /**
     * 经费卡号集合
     */
    private List<String> cardNoList;

    /**
     * 采购单号集合
     */
    private List<String> applicationNoList;

    /**
     * 出入库状态
     * {@link com.ruijing.store.order.api.base.enums.InventoryStatusEnum}
     */
    private List<Integer> inventoryStatusList;

    /**
     * 经费状态集合
     */
    private List<Integer> fundStatusList;

    /**
     * 排除的 经费状态集合
     */
    private List<Integer> excludeFundStatusList;

    /**
     * 订单 detail id 集合，单次查询的容量不可超过100个
     */
    private List<Integer> orderDetailIdList;

    /**
     * 关联关系集合，中大会用到的字段
     */
    private List<String> relateInfoList;

    /**
     * 存在关联关系,1-是，0-否
     */
    private Integer relateInfo;

    /**
     * 商品退货状态
     * {@link com.ruijing.store.order.api.base.enums.OrderDetailReturnStatus}
     */
    private List<Integer> detailReturnStatusList;

    /**
     * 排除的 订单号
     */
    private List<String> excludeOrderNoList;

    /**
     * 范围查询集合
     */
    private List<FieldRangeDTO> fieldRangeList;

    /**
     * 字段排序集合
     */
    private List<FieldSortDTO> fieldSortList;

    /**
     * 全文检索关键字
     */
    private String searchKey;

    /**
     * 全文订单详情检索关键字
     */
    private String detailSearchKey;

    /**
     * orderMaster全文检索字段
     */
    private Set<String> fullTextMasterFields = new HashSet<>();

    /**
     * orderDetail全文检索字段
     */
    private Set<String> fullTextDetailFields = new HashSet<>();

    /**
     * 是否根据 特定订单状态 排序 （供应商用）
     */
    private boolean scoreByStatus;

    /**
     * 管制品是否备案，0否，1是
     */
    private Integer confirmed;

    /**
     * 额外逻辑需要控制返回的订单id列表
     */
    private List<Integer> includeOrderIdList;

    /**
     * 自定义出参字段
     */
    private Set<String> sourceFieldSet = new HashSet<>();

    /**
     * 下单时填写的收货人姓名
     */
    private String buyerContactMan;

    /**
     * 订单类型（来源），orderTypeEnum
     */
    private Integer orderType;

    /**
     * 订单类型（来源）列表，orderTypeEnum
     */
    private List<Integer> orderTypeList;

    /**
     * 审批流id列表
     */
    private List<Integer> flowIdList;

    /**
     * 需要的一级分类筛选列表
     */
    private List<Integer> firstCategoryIdList;

    /**
     * 需要的二级分类筛选列表
     */
    private List<Integer> secondCategoryIdList;

    /**
     * 需要的三级分类筛选列表
     */
    private List<Integer> thirdCategoryIdList;

    /**
     * 额外信息 枚举
     */
    private List<OrderExtraInfoParamDTO> orderExtraInfoList;

    /**
     * 订单标签，对应 OrderExtraEnum.ORDER_TAG，用于筛选特定标签的订单
     * {@link com.ruijing.store.order.api.base.enums.OrderTagEnum}
     */
    private Integer orderTag;

    /**
     * {@link com.ruijing.shop.category.api.enums.InboundTypeEnum}
     * 1,试剂;2,耗材;3,服务;4,动物;5,工业品
     */
    @RpcModelProperty("分类标签,1,试剂;2,耗材;3,服务;4,动物;5,工业品")
    private Integer categoryTag;

    /**
     * 是否代配送的单，仅true为开启代配送，否则不开启
     */
    private Boolean deliveryProxyOn;

    /**
     * 采购父一级部门id列表
     */
    private List<Integer> deptParentIdList;

    /**
     * 采购父一级部门名称列表
     */
    private List<String> deptParentNameList;

    /**
     * 经费卡的院区编码
     */
    private String campusCode;

    /**
     * 经费卡的院区名称
     */
    private String campusName;

    /**
     * 需排除的退货状态列表
     */
    private List<Integer> excludeReturnStatusList;

    /**
     * 代配送状态
     */
    private Integer deliveryStatus;

    /**
     * 代配送订单分拣员
     */
    private String sortedUser;

    /**
     * 代配送订单配送员
     */
    private String deliveryUser;

    /**
     * 移动端代配送列表搜索关键字
     */
    private String wxDeliveryProxyKeyword;

    /**
     * 代配送订单操作人id
     */
    private String deliveryOperatorGuid;

    /**
     * 起始配送时间
     */
    private Date deliveredTimeStart;

    /**
     * 结束配送时间
     */
    private Date deliveredTimeEnd;

    /**
     * 配送类型
     */
    private List<Integer> deliveryTypeList;

    /**
     * 经费类型
     */
    private Integer fundType;

    /**
     * 验收审批等级
     */
    private Integer acceptApproveLevel;

    /**
     * 验收人
     */
    private String receiveMan;

    /**
     * 订单是否走自结算流程
     */
    private Boolean useStatement;

    @RpcModelProperty("商品搜索字段，商品名称、cas号或货号")
    private String productSearchContent;

    /**
     * 商品平台唯一编码
     */
    private String productCode;

    /**
     * 竞价单号列表
     */
    private List<String> bidOrderIdList;

    /**
     * 排除的竞价单号列表
     */
    private List<String> excludeBidOrderIdList;

    /**
     * 最后一级经费卡ID
     */
    private List<String> lastLevelCardIdList;

    /**
     * 是否常客购买（快照，下单那一刻）,0-否，1-是
     */
    private Integer regularCustomerPurchase;

    /**
     * 是否用户关注了该商家（快照，下单那一刻）,0-否，1-是
     */
    private Integer customerSubscribeSupp;

    public List<Integer> getDeliveryTypeList() {
        return deliveryTypeList;
    }

    public void setDeliveryTypeList(List<Integer> deliveryTypeList) {
        this.deliveryTypeList = deliveryTypeList;
    }

    public Date getDeliveredTimeStart() {
        return deliveredTimeStart;
    }

    public void setDeliveredTimeStart(Date deliveredTimeStart) {
        this.deliveredTimeStart = deliveredTimeStart;
    }

    public Date getDeliveredTimeEnd() {
        return deliveredTimeEnd;
    }

    public void setDeliveredTimeEnd(Date deliveredTimeEnd) {
        this.deliveredTimeEnd = deliveredTimeEnd;
    }

    public String getDeliveryOperatorGuid() {
        return deliveryOperatorGuid;
    }

    public void setDeliveryOperatorGuid(String deliveryOperatorGuid) {
        this.deliveryOperatorGuid = deliveryOperatorGuid;
    }

    public String getWxDeliveryProxyKeyword() {
        return wxDeliveryProxyKeyword;
    }

    public void setWxDeliveryProxyKeyword(String wxDeliveryProxyKeyword) {
        this.wxDeliveryProxyKeyword = wxDeliveryProxyKeyword;
    }

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public String getSortedUser() {
        return sortedUser;
    }

    public void setSortedUser(String sortedUser) {
        this.sortedUser = sortedUser;
    }

    public String getDeliveryUser() {
        return deliveryUser;
    }

    public void setDeliveryUser(String deliveryUser) {
        this.deliveryUser = deliveryUser;
    }

    public List<Integer> getStatementStatusList() {
        return statementStatusList;
    }

    public void setStatementStatusList(List<Integer> statementStatusList) {
        this.statementStatusList = statementStatusList;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getBidOrderId() {
        return bidOrderId;
    }

    public void setBidOrderId(String bidOrderId) {
        this.bidOrderId = bidOrderId;
    }

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public List<String> getApproveStatusList() {
        return approveStatusList;
    }

    public void setApproveStatusList(List<String> approveStatusList) {
        this.approveStatusList = approveStatusList;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public SortOrderEnum getOrderDateSort() {
        return orderDateSort;
    }

    public void setOrderDateSort(SortOrderEnum orderDateSort) {
        this.orderDateSort = orderDateSort;
    }

    public List<Integer> getStatementIdList() {
        return statementIdList;
    }

    public void setStatementIdList(List<Integer> statementIdList) {
        this.statementIdList = statementIdList;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public List<Integer> getExcludeStatusList() {
        return excludeStatusList;
    }

    public void setExcludeStatusList(List<Integer> excludeStatusList) {
        this.excludeStatusList = excludeStatusList;
    }

    /**
     * 增加排除状态的方法（防止外部底层为数组的list传入或者逻辑覆盖）
     * @param excludeStatusList
     */
    public void addExcludeStatusList(List<Integer> excludeStatusList) {
        this.excludeStatusList.addAll(excludeStatusList);
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public List<Integer> getDockingStatus() {
        return dockingStatus;
    }

    public void setDockingStatus(List<Integer> dockingStatus) {
        this.dockingStatus = dockingStatus;
    }

    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public void setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
    }

    public ProcessSpeciesEnum getSpecies() {
        return species;
    }

    public void setSpecies(ProcessSpeciesEnum species) {
        this.species = species;
    }

    public List<Integer> getApplicationIdList() {
        return applicationIdList;
    }

    public void setApplicationIdList(List<Integer> applicationIdList) {
        this.applicationIdList = applicationIdList;
    }

    public List<Integer> getExcludeApplicationIds() {
        return excludeApplicationIds;
    }

    public OrderSearchParamDTO setExcludeApplicationIds(List<Integer> excludeApplicationIds) {
        this.excludeApplicationIds = excludeApplicationIds;
        return this;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public List<Integer> getDepartmentIdList() {
        return departmentIdList;
    }

    public void setDepartmentIdList(List<Integer> departmentIdList) {
        this.departmentIdList = departmentIdList;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public List<Integer> getOrgIdList() {
        return orgIdList;
    }

    public void setOrgIdList(List<Integer> orgIdList) {
        this.orgIdList = orgIdList;
    }

    public List<Integer> getExcludeOrgIdList() {
        return excludeOrgIdList;
    }

    public void setExcludeOrgIdList(List<Integer> excludeOrgIdList) {
        this.excludeOrgIdList = excludeOrgIdList;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public List<Integer> getBuyerIdList() {
        return buyerIdList;
    }

    public void setBuyerIdList(List<Integer> buyerIdList) {
        this.buyerIdList = buyerIdList;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public List<Integer> getSuppIdList() {
        return suppIdList;
    }

    public void setSuppIdList(List<Integer> suppIdList) {
        this.suppIdList = suppIdList;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public List<FieldRangeDTO> getFieldRangeList() {
        return fieldRangeList;
    }

    public void setFieldRangeList(List<FieldRangeDTO> fieldRangeList) {
        this.fieldRangeList = fieldRangeList;
    }

    public List<FieldSortDTO> getFieldSortList() {
        return fieldSortList;
    }

    public void setFieldSortList(List<FieldSortDTO> fieldSortList) {
        this.fieldSortList = fieldSortList;
    }

    public String getSearchKey() {
        return searchKey;
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }

    public String getDetailSearchKey() {
        return detailSearchKey;
    }

    public void setDetailSearchKey(String detailSearchKey) {
        this.detailSearchKey = detailSearchKey;
    }

    public Set<String> getFullTextMasterFields() {
        return fullTextMasterFields;
    }

    public void addFullTextMasterFields(String orderMasterField) {
        this.fullTextMasterFields.add(orderMasterField);
    }

    public Set<String> getFullTextDetailFields() {
        return fullTextDetailFields;
    }

    public void addFullTextDetailFields(String orderDetailField) {
        this.fullTextDetailFields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName() + "." + orderDetailField);
    }

    public boolean getScoreByStatus() {
        return scoreByStatus;
    }

    public void setScoreByStatus(boolean scoreByStatus) {
        this.scoreByStatus = scoreByStatus;
    }

    public String getProductSn() {
        return productSn;
    }

    public void setProductSn(String productSn) {
        this.productSn = productSn;
    }

    public List<Integer> getDetailReturnStatusList() {
        return detailReturnStatusList;
    }

    public void setDetailReturnStatusList(List<Integer> detailReturnStatusList) {
        this.detailReturnStatusList = detailReturnStatusList;
    }

    public List<String> getExcludeOrderNoList() {
        return excludeOrderNoList;
    }

    public void setExcludeOrderNoList(List<String> excludeOrderNoList) {
        this.excludeOrderNoList = excludeOrderNoList;
    }

    public List<String> getCardNoList() {
        return cardNoList;
    }

    public void setCardNoList(List<String> cardNoList) {
        this.cardNoList = cardNoList;
    }

    public List<String> getApplicationNoList() {
        return applicationNoList;
    }

    public void setApplicationNoList(List<String> applicationNoList) {
        this.applicationNoList = applicationNoList;
    }

    public List<String> getRelateInfoList() {
        return relateInfoList;
    }

    public void setRelateInfoList(List<String> relateInfoList) {
        this.relateInfoList = relateInfoList;
    }

    public List<Integer> getFundStatusList() {
        return fundStatusList;
    }

    public void setFundStatusList(List<Integer> fundStatusList) {
        this.fundStatusList = fundStatusList;
    }

    public List<Integer> getExcludeFundStatusList() {
        return excludeFundStatusList;
    }

    public void setExcludeFundStatusList(List<Integer> excludeFundStatusList) {
        this.excludeFundStatusList = excludeFundStatusList;
    }

    public List<String> getCardIdList() {
        return cardIdList;
    }

    public void setCardIdList(List<String> cardIdList) {
        this.cardIdList = cardIdList;
    }

    public Set<String> getSourceFieldSet() {
        return sourceFieldSet;
    }

    public void addSourceField(String field) {
        this.sourceFieldSet.add(field);
    }

    public List<Integer> getInventoryStatusList() {
        return inventoryStatusList;
    }

    public void setInventoryStatusList(List<Integer> inventoryStatusList) {
        this.inventoryStatusList = inventoryStatusList;
    }

    public List<Integer> getOrderDetailIdList() {
        return orderDetailIdList;
    }

    public void setOrderDetailIdList(List<Integer> orderDetailIdList) {
        this.orderDetailIdList = orderDetailIdList;
    }

    public Integer getConfirmed() {
        return confirmed;
    }

    public void setConfirmed(Integer confirmed) {
        this.confirmed = confirmed;
    }

    public List<Integer> getIncludeOrderIdList() {
        return includeOrderIdList;
    }

    public void setIncludeOrderIdList(List<Integer> includeOrderIdList) {
        this.includeOrderIdList = includeOrderIdList;
    }

    public String getBuyerContactMan() {
        return buyerContactMan;
    }

    public OrderSearchParamDTO setBuyerContactMan(String buyerContactMan) {
        this.buyerContactMan = buyerContactMan;
        return this;
    }

    public List<Integer> getOrderTypeList() {
        return orderTypeList;
    }

    public void setOrderTypeList(List<Integer> orderTypeList) {
        this.orderTypeList = orderTypeList;
    }

    public List<Integer> getFlowIdList() {
        return flowIdList;
    }

    public void setFlowIdList(List<Integer> flowIdList) {
        this.flowIdList = flowIdList;
    }

    public List<Integer> getFirstCategoryIdList() {
        return firstCategoryIdList;
    }

    public void setFirstCategoryIdList(List<Integer> firstCategoryIdList) {
        this.firstCategoryIdList = firstCategoryIdList;
    }

    public List<Integer> getSecondCategoryIdList() {
        return secondCategoryIdList;
    }

    public void setSecondCategoryIdList(List<Integer> secondCategoryIdList) {
        this.secondCategoryIdList = secondCategoryIdList;
    }

    public List<Integer> getThirdCategoryIdList() {
        return thirdCategoryIdList;
    }

    public void setThirdCategoryIdList(List<Integer> thirdCategoryIdList) {
        this.thirdCategoryIdList = thirdCategoryIdList;
    }

    public String getCasNo() {
        return casNo;
    }

    public OrderSearchParamDTO setCasNo(String casNo) {
        this.casNo = casNo;
        return this;
    }

    public Boolean getOrgNameFullMatch() {
        return orgNameFullMatch;
    }

    public void setOrgNameFullMatch(Boolean orgNameFullMatch) {
        this.orgNameFullMatch = orgNameFullMatch;
    }

    public List<OrderExtraInfoParamDTO> getOrderExtraInfoList() {
        return orderExtraInfoList;
    }

    public void setOrderExtraInfoList(List<OrderExtraInfoParamDTO> orderExtraInfoList) {
        this.orderExtraInfoList = orderExtraInfoList;
    }

    public Integer getOrderTag() {
        return orderTag;
    }

    public void setOrderTag(Integer orderTag) {
        this.orderTag = orderTag;
    }

    public Integer getCategoryTag() {
        return categoryTag;
    }

    public void setCategoryTag(Integer categoryTag) {
        this.categoryTag = categoryTag;
    }

    public Boolean getDeliveryProxyOn() {
        return deliveryProxyOn;
    }

    public void setDeliveryProxyOn(Boolean deliveryProxyOn) {
        this.deliveryProxyOn = deliveryProxyOn;
    }

    public List<Integer> getDeptParentIdList() {
        return deptParentIdList;
    }

    public void setDeptParentIdList(List<Integer> deptParentIdList) {
        this.deptParentIdList = deptParentIdList;
    }

    public List<String> getDeptParentNameList() {
        return deptParentNameList;
    }

    public void setDeptParentNameList(List<String> deptParentNameList) {
        this.deptParentNameList = deptParentNameList;
    }

    public String getCampusCode() {
        return campusCode;
    }

    public void setCampusCode(String campusCode) {
        this.campusCode = campusCode;
    }

    public String getCampusName() {
        return campusName;
    }

    public void setCampusName(String campusName) {
        this.campusName = campusName;
    }

    public List<Integer> getExcludeReturnStatusList() {
        return excludeReturnStatusList;
    }

    public void setExcludeReturnStatusList(List<Integer> excludeReturnStatusList) {
        this.excludeReturnStatusList = excludeReturnStatusList;
    }

    public Integer getFundType() {
        return fundType;
    }

    public void setFundType(Integer fundType) {
        this.fundType = fundType;
    }

    public Integer getAcceptApproveLevel() {
        return acceptApproveLevel;
    }

    public void setAcceptApproveLevel(Integer acceptApproveLevel) {
        this.acceptApproveLevel = acceptApproveLevel;
    }

    public String getReceiveMan() {
        return receiveMan;
    }

    public OrderSearchParamDTO setReceiveMan(String receiveMan) {
        this.receiveMan = receiveMan;
        return this;
    }

    public Boolean getUseStatement() {
        return useStatement;
    }

    public void setUseStatement(Boolean useStatement) {
        this.useStatement = useStatement;
    }

    public String getProductSearchContent() {
        return productSearchContent;
    }

    public void setProductSearchContent(String productSearchContent) {
        this.productSearchContent = productSearchContent;
    }

    public String getProductCode() {
        return productCode;
    }

    public OrderSearchParamDTO setProductCode(String productCode) {
        this.productCode = productCode;
        return this;
    }

    public List<String> getBidOrderIdList() {
        return bidOrderIdList;
    }

    public void setBidOrderIdList(List<String> bidOrderIdList) {
        this.bidOrderIdList = bidOrderIdList;
    }

    public List<String> getExcludeBidOrderIdList() {
        return excludeBidOrderIdList;
    }

    public OrderSearchParamDTO setExcludeBidOrderIdList(List<String> excludeBidOrderIdList) {
        this.excludeBidOrderIdList = excludeBidOrderIdList;
        return this;
    }

    public List<String> getLastLevelCardIdList() {
        return lastLevelCardIdList;
    }

    public OrderSearchParamDTO setLastLevelCardIdList(List<String> lastLevelCardIdList) {
        this.lastLevelCardIdList = lastLevelCardIdList;
        return this;
    }

    public Integer getRegularCustomerPurchase() {
        return regularCustomerPurchase;
    }

    public OrderSearchParamDTO setRegularCustomerPurchase(Integer regularCustomerPurchase) {
        this.regularCustomerPurchase = regularCustomerPurchase;
        return this;
    }

    public Integer getCustomerSubscribeSupp() {
        return customerSubscribeSupp;
    }

    public OrderSearchParamDTO setCustomerSubscribeSupp(Integer customerSubscribeSupp) {
        this.customerSubscribeSupp = customerSubscribeSupp;
        return this;
    }

    public Integer getRelateInfo() {
        return relateInfo;
    }

    public OrderSearchParamDTO setRelateInfo(Integer relateInfo) {
        this.relateInfo = relateInfo;
        return this;
    }

    @Override
    public String toString() {
        return "OrderSearchParamDTO{" +
                "orderId=" + orderId +
                ", bidOrderId='" + bidOrderId + '\'' +
                ", orderIdList=" + orderIdList +
                ", approveStatusList=" + approveStatusList +
                ", operatorId='" + operatorId + '\'' +
                ", statementIdList=" + statementIdList +
                ", orderDateSort=" + orderDateSort +
                ", statusList=" + statusList +
                ", excludeStatusList=" + excludeStatusList +
                ", statementStatusList=" + statementStatusList +
                ", orderNo='" + orderNo + '\'' +
                ", dockingStatus=" + dockingStatus +
                ", orderNoList=" + orderNoList +
                ", species=" + species +
                ", applicationIdList=" + applicationIdList +
                ", excludeApplicationIds=" + excludeApplicationIds +
                ", departmentName='" + departmentName + '\'' +
                ", departmentIdList=" + departmentIdList +
                ", orgCode='" + orgCode + '\'' +
                ", orgName='" + orgName + '\'' +
                ", orgNameFullMatch=" + orgNameFullMatch +
                ", orgIdList=" + orgIdList +
                ", excludeOrgIdList=" + excludeOrgIdList +
                ", buyerName='" + buyerName + '\'' +
                ", buyerIdList=" + buyerIdList +
                ", suppName='" + suppName + '\'' +
                ", suppIdList=" + suppIdList +
                ", goodsName='" + goodsName + '\'' +
                ", goodsCode='" + goodsCode + '\'' +
                ", casNo='" + casNo + '\'' +
                ", brandId=" + brandId +
                ", brandName='" + brandName + '\'' +
                ", productSn='" + productSn + '\'' +
                ", cardIdList=" + cardIdList +
                ", cardNoList=" + cardNoList +
                ", applicationNoList=" + applicationNoList +
                ", inventoryStatusList=" + inventoryStatusList +
                ", fundStatusList=" + fundStatusList +
                ", excludeFundStatusList=" + excludeFundStatusList +
                ", orderDetailIdList=" + orderDetailIdList +
                ", relateInfoList=" + relateInfoList +
                ", relateInfo=" + relateInfo +
                ", detailReturnStatusList=" + detailReturnStatusList +
                ", excludeOrderNoList=" + excludeOrderNoList +
                ", fieldRangeList=" + fieldRangeList +
                ", fieldSortList=" + fieldSortList +
                ", searchKey='" + searchKey + '\'' +
                ", detailSearchKey='" + detailSearchKey + '\'' +
                ", fullTextMasterFields=" + fullTextMasterFields +
                ", fullTextDetailFields=" + fullTextDetailFields +
                ", scoreByStatus=" + scoreByStatus +
                ", confirmed=" + confirmed +
                ", includeOrderIdList=" + includeOrderIdList +
                ", sourceFieldSet=" + sourceFieldSet +
                ", buyerContactMan='" + buyerContactMan + '\'' +
                ", orderType=" + orderType +
                ", orderTypeList=" + orderTypeList +
                ", flowIdList=" + flowIdList +
                ", firstCategoryIdList=" + firstCategoryIdList +
                ", secondCategoryIdList=" + secondCategoryIdList +
                ", thirdCategoryIdList=" + thirdCategoryIdList +
                ", orderExtraInfoList=" + orderExtraInfoList +
                ", categoryTag=" + categoryTag +
                ", deliveryProxyOn=" + deliveryProxyOn +
                ", deptParentIdList=" + deptParentIdList +
                ", deptParentNameList=" + deptParentNameList +
                ", campusCode='" + campusCode + '\'' +
                ", campusName='" + campusName + '\'' +
                ", excludeReturnStatusList=" + excludeReturnStatusList +
                ", deliveryStatus=" + deliveryStatus +
                ", sortedUser='" + sortedUser + '\'' +
                ", deliveryUser='" + deliveryUser + '\'' +
                ", wxDeliveryProxyKeyword='" + wxDeliveryProxyKeyword + '\'' +
                ", deliveryOperatorGuid='" + deliveryOperatorGuid + '\'' +
                ", deliveredTimeStart=" + deliveredTimeStart +
                ", deliveredTimeEnd=" + deliveredTimeEnd +
                ", deliveryTypeList=" + deliveryTypeList +
                ", fundType=" + fundType +
                ", acceptApproveLevel=" + acceptApproveLevel +
                ", receiveMan='" + receiveMan + '\'' +
                ", useStatement=" + useStatement +
                ", productSearchContent='" + productSearchContent + '\'' +
                ", productCode='" + productCode + '\'' +
                ", bidOrderIdList=" + bidOrderIdList +
                ", excludeBidOrderIdList=" + excludeBidOrderIdList +
                ", lastLevelCardIdList=" + lastLevelCardIdList +
                ", regularCustomerPurchase=" + regularCustomerPurchase +
                ", customerSubscribeSupp=" + customerSubscribeSupp +
                '}';
    }
}
